import { useState } from "react"
import { Chip } from "@mui/material"

const FormHistoryChip = ({ field, index }) => {
  const [isActive, setIsActive] = useState(false)

  return (
    <Chip
      label={field.fieldKey || field.name || `Field ${index + 1}`}
      sx={{
        "&.MuiChip-root": {
          margin: "8px",
          width: "30%",
          backgroundColor: isActive ? "#0c2d5f" : "#ebebeb",
          color: isActive ? "white" : "#666666",
          fontWeight: isActive ? "bold" : "normal",
          textAlign: "center",
          height: "auto",
          "& .MuiChip-label": {
            overflow: "visible",
            textOverflow: "unset",
            whiteSpace: "normal",
            wordBreak: "break-word",
            display: "block",
            padding: "8px 12px",
            lineHeight: 1.2
          }
        }
      }}
      onClick={() => setIsActive(!isActive)}
    />
  )
}

export default FormHistoryChip
