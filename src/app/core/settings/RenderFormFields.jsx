import { Typography, Stack } from "@mui/material"
import FormHistoryChip from "./FormHistoryChip"

const RenderFormFields = ({ formFields }) => {
  if (!formFields || formFields.length === 0) {
    return (
      <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary", padding: "10px" }}>
        No form fields found
      </Typography>
    )
  }

  return (
    <Stack direction="row" flexWrap="wrap" spacing={1} sx={{ padding: "10px" }}>
      {formFields.map((field, index) => (
        <FormHistoryChip key={index} field={field} index={index} />
      ))}
    </Stack>
  )
}

export default RenderFormFields
