import { useState } from "react"
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Stack
} from "@mui/material"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import { useQueryClient } from "@tanstack/react-query"
import { getFunctions, httpsCallable } from "firebase/functions"
import AlertSnackbar from "@/components/AlertSnackBar"

import RenderFormStructure from "./RenderFormStructure.jsx"

const FormHistorySettings = () => {
  const [expandedAccordion, setExpandedAccordion] = useState(false)
  const [formStructures, setFormStructures] = useState({})
  const [loadingStructures, setLoadingStructures] = useState({})
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"]) || []

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleAccordionChange = async (panel, projectTypeId) => {
    const isExpanding = expandedAccordion !== panel

    setExpandedAccordion(isExpanding ? panel : false)

    // If expanding and we don't have the form structure yet, fetch it
    if (isExpanding && !formStructures[projectTypeId]) {
      setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: true }))

      try {
        const functions = getFunctions()
        const getProjectTypeFormStructure = httpsCallable(functions, "getProjectTypeFormStructure")

        const result = await getProjectTypeFormStructure({ projectTypeId })

        setFormStructures((prev) => ({
          ...prev,
          [projectTypeId]: result.data
        }))
      } catch (error) {
        console.error("Failed to fetch form structure:", error)
        setAlertInfo({
          open: true,
          message: `Error loading form structure: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      } finally {
        setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: false }))
      }
    }
  }

  return (
    <Stack direction="column" sx={{ margin: "50px", height: "calc(100vh - 450px)" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Form History
      </Typography>

      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        View form structures for each project type. Click on a project type to expand and see its form
        structure.
      </Typography>

      <Stack
        flexGrow={1}
        sx={{
          overflowY: "auto",
          paddingRight: "8px",
          paddingBottom: "20px",
          "&::-webkit-scrollbar": {
            width: "8px"
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#a8a8a8"
          }
        }}
      >
        {projectTypeList.length === 0 ? (
          <Typography variant="body1" sx={{ textAlign: "center", padding: "40px" }}>
            No project types available.
          </Typography>
        ) : (
          <Stack spacing={1}>
            {projectTypeList.map((projectType) => {
              const projectTypeId = projectType.projectTypeId.native
              const panelId = `panel-${projectTypeId}`
              const isLoading = loadingStructures[projectTypeId]
              const formStructure = formStructures[projectTypeId]

              return (
                <Accordion
                  key={projectTypeId}
                  expanded={expandedAccordion === panelId}
                  onChange={() => handleAccordionChange(panelId, projectTypeId)}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${panelId}-content`}
                    id={`${panelId}-header`}
                  >
                    <Typography variant="h6">{projectType.name}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {isLoading ? (
                      <Stack direction="row" alignItems="center" spacing={2} sx={{ padding: "20px" }}>
                        <CircularProgress size={24} />
                        <Typography variant="body2">Loading form structure...</Typography>
                      </Stack>
                    ) : formStructure ? (
                      <Box sx={{ padding: "10px 0" }}>
                        <RenderFormStructure structure={formStructure} />
                      </Box>
                    ) : (
                      <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary" }}>
                        Click to load form structure for this project type.
                      </Typography>
                    )}
                  </AccordionDetails>
                </Accordion>
              )
            })}
          </Stack>
        )}
      </Stack>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default FormHistorySettings
