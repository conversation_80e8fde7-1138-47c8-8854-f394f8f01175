import { useState } from "react"
import { Box, Typography, CircularProgress, Stack, NativeSelect } from "@mui/material"
import { useQueryClient } from "@tanstack/react-query"
import { getFunctions, httpsCallable } from "firebase/functions"
import AlertSnackbar from "@/components/AlertSnackBar"

import RenderFormStructure from "./RenderFormStructure"

const FormHistorySettings = () => {
  const [selectedProjectType, setSelectedProjectType] = useState("")
  const [formStructures, setFormStructures] = useState({})
  const [loadingStructures, setLoadingStructures] = useState({})
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"]) || []

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleProjectTypeChange = async (event) => {
    const projectTypeId = event.target.value
    setSelectedProjectType(projectTypeId)

    // If we don't have the field structure yet, fetch it
    if (projectTypeId && projectTypeId !== "" && !formStructures[projectTypeId]) {
      setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: true }))

      try {
        const functions = getFunctions()
        const getProjectTypeFormStructure = httpsCallable(functions, "getProjectTypeFormStructure")

        const result = await getProjectTypeFormStructure({ projectTypeId })

        setFormStructures((prev) => ({
          ...prev,
          [projectTypeId]: result.data
        }))
      } catch (error) {
        console.error("Failed to fetch field structure:", error)
        setAlertInfo({
          open: true,
          message: `Error loading field structure: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      } finally {
        setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: false }))
      }
    }
  }

  const isLoading = loadingStructures[selectedProjectType]
  const formStructure = formStructures[selectedProjectType]

  return (
    <Stack direction="column" sx={{ margin: "50px", height: "calc(100vh - 450px)" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Field History
      </Typography>

      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        View field structures for each project type. Select a project type to see its field structure.
      </Typography>

      <Box sx={{ width: 250, marginBottom: "30px" }}>
        <NativeSelect sx={{ width: 200 }} value={selectedProjectType} onChange={handleProjectTypeChange}>
          <option value="">Select Project Type</option>
          {projectTypeList.map((projectType) => (
            <option key={projectType.projectTypeId.native} value={projectType.projectTypeId.native}>
              {projectType.name}
            </option>
          ))}
        </NativeSelect>
      </Box>

      <Stack
        flexGrow={1}
        sx={{
          overflowY: "auto",
          paddingRight: "8px",
          paddingBottom: "20px",
          "&::-webkit-scrollbar": {
            width: "8px"
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#a8a8a8"
          }
        }}
      >
        {projectTypeList.length === 0 ? (
          <Typography variant="body1" sx={{ textAlign: "center", padding: "40px" }}>
            No project types available.
          </Typography>
        ) : !selectedProjectType ? (
          <Typography variant="body1" sx={{ textAlign: "center", padding: "40px", color: "text.secondary" }}>
            Please select a project type to view its field structure.
          </Typography>
        ) : isLoading ? (
          <Stack
            direction="row"
            alignItems="center"
            justifyContent="center"
            spacing={2}
            sx={{ padding: "40px" }}
          >
            <CircularProgress size={24} />
            <Typography variant="body2">Loading field structure...</Typography>
          </Stack>
        ) : formStructure ? (
          <Box sx={{ padding: "10px 0" }}>
            <RenderFormStructure structure={formStructure} />
          </Box>
        ) : (
          <Typography
            variant="body2"
            sx={{ textAlign: "center", padding: "40px", fontStyle: "italic", color: "text.secondary" }}
          >
            No field structure available for this project type.
          </Typography>
        )}
      </Stack>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default FormHistorySettings
