import { useState } from "react"
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  CircularProgress,
  Stack,
  Chip
} from "@mui/material"
import ExpandMoreIcon from "@mui/icons-material/ExpandMore"
import { useQueryClient } from "@tanstack/react-query"
import { getFunctions, httpsCallable } from "firebase/functions"
import AlertSnackbar from "@/components/AlertSnackBar"

const FormHistoryChip = ({ field }) => {
  const [isActive, setIsActive] = useState(false)

  return (
    <Chip
      label={field.fieldKey || field.name || `Field ${index + 1}`}
      sx={{
        "&.MuiChip-root": {
          margin: "8px",
          width: "30%",
          backgroundColor: isActive ? "#0c2d5f" : "#ebebeb",
          color: isActive ? "white" : "#666666",
          fontWeight: isActive ? "bold" : "normal",
          textAlign: "center",
          height: "auto",
          "& .MuiChip-label": {
            overflow: "visible",
            textOverflow: "unset",
            whiteSpace: "normal",
            wordBreak: "break-word",
            display: "block",
            padding: "8px 12px",
            lineHeight: 1.2
          }
        }
      }}
      onClick={() => setIsActive(!isActive)}
    />
  )
}

const FormHistorySettings = () => {
  const [expandedAccordion, setExpandedAccordion] = useState(false)
  const [formStructures, setFormStructures] = useState({})
  const [loadingStructures, setLoadingStructures] = useState({})
  const [alertInfo, setAlertInfo] = useState({ open: false, message: "", severity: "success" })

  const queryClient = useQueryClient()
  const projectTypeList = queryClient.getQueryData(["projectTypeList"]) || []

  const handleAlertClose = () => {
    setAlertInfo((prev) => ({ ...prev, open: false }))
  }

  const handleAccordionChange = async (panel, projectTypeId) => {
    const isExpanding = expandedAccordion !== panel

    setExpandedAccordion(isExpanding ? panel : false)

    // If expanding and we don't have the form structure yet, fetch it
    if (isExpanding && !formStructures[projectTypeId]) {
      setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: true }))

      try {
        const functions = getFunctions()
        const getProjectTypeFormStructure = httpsCallable(functions, "getProjectTypeFormStructure")

        const result = await getProjectTypeFormStructure({ projectTypeId })

        setFormStructures((prev) => ({
          ...prev,
          [projectTypeId]: result.data
        }))
      } catch (error) {
        console.error("Failed to fetch form structure:", error)
        setAlertInfo({
          open: true,
          message: `Error loading form structure: ${error.message || "Unknown error"}`,
          severity: "error"
        })
      } finally {
        setLoadingStructures((prev) => ({ ...prev, [projectTypeId]: false }))
      }
    }
  }

  const renderFormFields = (formFields) => {
    if (!formFields || formFields.length === 0) {
      return (
        <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary", padding: "10px" }}>
          No form fields found
        </Typography>
      )
    }

    return (
      <Stack direction="row" flexWrap="wrap" spacing={1} sx={{ padding: "10px" }}>
        {formFields.map((field, index) => (
          <FormHistoryChip key={index} field={field} />
        ))}
      </Stack>
    )
  }

  const renderFormStructure = (structure) => {
    if (!structure) return null

    // Handle the expected structure format
    if (structure.sections && Array.isArray(structure.sections)) {
      return (
        <Stack spacing={1}>
          {structure.sections.map((section, index) => (
            <Accordion key={index} sx={{ backgroundColor: "#fafafa", f }}>
              <AccordionSummary
                expandIcon={<ExpandMoreIcon />}
                aria-controls={`section-${index}-content`}
                id={`section-${index}-header`}
              >
                <Typography variant="subtitle1" sx={{ fontWeight: "medium" }}>
                  {section.name || section.sectionSelector || `Section ${index + 1}`}
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                {section.error ? (
                  <Typography variant="body2" sx={{ color: "warning.main", fontStyle: "italic" }}>
                    {section.error}
                  </Typography>
                ) : (
                  renderFormFields(section.formFields)
                )}
              </AccordionDetails>
            </Accordion>
          ))}
        </Stack>
      )
    }

    // Fallback for unexpected structure formats
    return (
      <Typography variant="body2" component="pre" sx={{ whiteSpace: "pre-wrap", fontSize: "0.75rem" }}>
        {JSON.stringify(structure, null, 2)}
      </Typography>
    )
  }

  return (
    <Stack direction="column" sx={{ margin: "50px", height: "calc(100vh - 450px)" }}>
      <Typography variant="h4" sx={{ marginBottom: "30px" }}>
        Form History
      </Typography>

      <Typography variant="body1" sx={{ marginBottom: "30px", color: "text.secondary" }}>
        View form structures for each project type. Click on a project type to expand and see its form
        structure.
      </Typography>

      <Stack
        flexGrow={1}
        sx={{
          overflowY: "auto",
          paddingRight: "8px",
          paddingBottom: "20px",
          "&::-webkit-scrollbar": {
            width: "8px"
          },
          "&::-webkit-scrollbar-track": {
            backgroundColor: "#f1f1f1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb": {
            backgroundColor: "#c1c1c1",
            borderRadius: "4px"
          },
          "&::-webkit-scrollbar-thumb:hover": {
            backgroundColor: "#a8a8a8"
          }
        }}
      >
        {projectTypeList.length === 0 ? (
          <Typography variant="body1" sx={{ textAlign: "center", padding: "40px" }}>
            No project types available.
          </Typography>
        ) : (
          <Stack spacing={1}>
            {projectTypeList.map((projectType) => {
              const projectTypeId = projectType.projectTypeId.native
              const panelId = `panel-${projectTypeId}`
              const isLoading = loadingStructures[projectTypeId]
              const formStructure = formStructures[projectTypeId]

              return (
                <Accordion
                  key={projectTypeId}
                  expanded={expandedAccordion === panelId}
                  onChange={() => handleAccordionChange(panelId, projectTypeId)}
                >
                  <AccordionSummary
                    expandIcon={<ExpandMoreIcon />}
                    aria-controls={`${panelId}-content`}
                    id={`${panelId}-header`}
                  >
                    <Typography variant="h6">{projectType.name}</Typography>
                  </AccordionSummary>
                  <AccordionDetails>
                    {isLoading ? (
                      <Stack direction="row" alignItems="center" spacing={2} sx={{ padding: "20px" }}>
                        <CircularProgress size={24} />
                        <Typography variant="body2">Loading form structure...</Typography>
                      </Stack>
                    ) : formStructure ? (
                      <Box sx={{ padding: "10px 0" }}>{renderFormStructure(formStructure)}</Box>
                    ) : (
                      <Typography variant="body2" sx={{ fontStyle: "italic", color: "text.secondary" }}>
                        Click to load form structure for this project type.
                      </Typography>
                    )}
                  </AccordionDetails>
                </Accordion>
              )
            })}
          </Stack>
        )}
      </Stack>

      <AlertSnackbar
        open={alertInfo.open}
        message={alertInfo.message}
        severity={alertInfo.severity}
        onClose={handleAlertClose}
        anchorOrigin={{ vertical: "bottom", horizontal: "right" }}
      />
    </Stack>
  )
}

export default FormHistorySettings
